{"name": "mallsurf-core", "version": "1.0.0", "private": false, "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"registry": "https://npm.pkg.github.com", "@mallsurf:registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/Mallsurf/mallsurf-core.git"}, "scripts": {"build": "prisma generate && tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@prisma/client": "^6.10.1", "axios": "^1.10.0", "clsx": "^2.1.1", "next": "15.3.4", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@jest/globals": "^30.0.2", "@types/jest": "^30.0.0", "@types/node": "^20", "jest": "^30.0.2", "rimraf": "^5.0.0", "ts-jest": "^29.4.0", "typescript": "^5"}, "files": ["dist/**/*", "README.md"], "keywords": ["mallsurf", "core", "database", "utilities"]}