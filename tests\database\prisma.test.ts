import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';

// Mock PrismaClient before importing
const mockPrismaClient = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $transaction: jest.fn(),
  user: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  mall: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  shop: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  }
};

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient)
}));

describe('Prisma Client', () => {
  let prisma: any;

  beforeEach(async () => {
    jest.clearAllMocks();
    // Dynamically import after mocking
    const prismaModule = await import('../../src/database/prisma');
    prisma = prismaModule.default;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should be a PrismaClient instance', () => {
    expect(prisma).toBeDefined();
    expect(prisma.$connect).toBeDefined();
    expect(prisma.$disconnect).toBeDefined();
  });

  it('should have user model methods', () => {
    expect(prisma.user).toBeDefined();
    expect(prisma.user.findMany).toBeDefined();
    expect(prisma.user.findUnique).toBeDefined();
    expect(prisma.user.create).toBeDefined();
    expect(prisma.user.update).toBeDefined();
    expect(prisma.user.delete).toBeDefined();
  });

  it('should have mall model methods', () => {
    expect(prisma.mall).toBeDefined();
    expect(prisma.mall.findMany).toBeDefined();
    expect(prisma.mall.findUnique).toBeDefined();
    expect(prisma.mall.create).toBeDefined();
    expect(prisma.mall.update).toBeDefined();
    expect(prisma.mall.delete).toBeDefined();
  });

  it('should have shop model methods', () => {
    expect(prisma.shop).toBeDefined();
    expect(prisma.shop.findMany).toBeDefined();
    expect(prisma.shop.findUnique).toBeDefined();
    expect(prisma.shop.create).toBeDefined();
    expect(prisma.shop.update).toBeDefined();
    expect(prisma.shop.delete).toBeDefined();
  });

  it('should be able to connect', async () => {
    mockPrismaClient.$connect.mockResolvedValue(undefined);
    
    await prisma.$connect();
    
    expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(1);
  });

  it('should be able to disconnect', async () => {
    mockPrismaClient.$disconnect.mockResolvedValue(undefined);
    
    await prisma.$disconnect();
    
    expect(mockPrismaClient.$disconnect).toHaveBeenCalledTimes(1);
  });

  it('should support transactions', async () => {
    const mockTransactionResult = { id: '1', name: 'Test' };
    mockPrismaClient.$transaction.mockResolvedValue(mockTransactionResult);
    
    const result = await prisma.$transaction(async (tx: any) => {
      return { id: '1', name: 'Test' };
    });
    
    expect(mockPrismaClient.$transaction).toHaveBeenCalledTimes(1);
    expect(result).toEqual(mockTransactionResult);
  });

  describe('User operations', () => {
    it('should find many users', async () => {
      const mockUsers = [
        { id: '1', name: 'User 1', email: '<EMAIL>' },
        { id: '2', name: 'User 2', email: '<EMAIL>' }
      ];
      mockPrismaClient.user.findMany.mockResolvedValue(mockUsers);
      
      const users = await prisma.user.findMany();
      
      expect(mockPrismaClient.user.findMany).toHaveBeenCalledTimes(1);
      expect(users).toEqual(mockUsers);
    });

    it('should find unique user', async () => {
      const mockUser = { id: '1', name: 'User 1', email: '<EMAIL>' };
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);
      
      const user = await prisma.user.findUnique({ where: { id: '1' } });
      
      expect(mockPrismaClient.user.findUnique).toHaveBeenCalledWith({ where: { id: '1' } });
      expect(user).toEqual(mockUser);
    });

    it('should create user', async () => {
      const userData = { name: 'New User', email: '<EMAIL>', password: 'password' };
      const mockCreatedUser = { id: '1', ...userData };
      mockPrismaClient.user.create.mockResolvedValue(mockCreatedUser);
      
      const user = await prisma.user.create({ data: userData });
      
      expect(mockPrismaClient.user.create).toHaveBeenCalledWith({ data: userData });
      expect(user).toEqual(mockCreatedUser);
    });
  });

  describe('Mall operations', () => {
    it('should find many malls', async () => {
      const mockMalls = [
        { id: '1', name: 'Mall 1', slug: 'mall-1' },
        { id: '2', name: 'Mall 2', slug: 'mall-2' }
      ];
      mockPrismaClient.mall.findMany.mockResolvedValue(mockMalls);
      
      const malls = await prisma.mall.findMany();
      
      expect(mockPrismaClient.mall.findMany).toHaveBeenCalledTimes(1);
      expect(malls).toEqual(mockMalls);
    });
  });
});
