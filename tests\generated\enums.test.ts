import { describe, it, expect } from '@jest/globals';
import { 
  ParkingType, 
  ReportType, 
  UserRole, 
  $Enums 
} from '../../src/index';

describe('Generated Enums', () => {
  describe('ParkingType enum', () => {
    it('should have correct values', () => {
      expect(ParkingType.free).toBe('free');
      expect(ParkingType.paid).toBe('paid');
      expect(ParkingType.valet).toBe('valet');
    });

    it('should have all expected keys', () => {
      const keys = Object.keys(ParkingType);
      expect(keys).toContain('free');
      expect(keys).toContain('paid');
      expect(keys).toContain('valet');
      expect(keys).toHaveLength(3);
    });

    it('should be usable in type guards', () => {
      const isValidParkingType = (value: string): value is ParkingType => {
        return Object.values(ParkingType).includes(value as ParkingType);
      };

      expect(isValidParkingType('free')).toBe(true);
      expect(isValidParkingType('paid')).toBe(true);
      expect(isValidParkingType('valet')).toBe(true);
      expect(isValidParkingType('invalid')).toBe(false);
    });
  });

  describe('UserRole enum', () => {
    it('should have correct values', () => {
      expect(UserRole.admin).toBe('admin');
      expect(UserRole.mall_manager).toBe('mall_manager');
      expect(UserRole.shop_owner).toBe('shop_owner');
      expect(UserRole.user).toBe('user');
    });

    it('should have all expected keys', () => {
      const keys = Object.keys(UserRole);
      expect(keys).toContain('admin');
      expect(keys).toContain('mall_manager');
      expect(keys).toContain('shop_owner');
      expect(keys).toContain('user');
      expect(keys).toHaveLength(4);
    });

    it('should be usable in role-based logic', () => {
      const hasAdminAccess = (role: UserRole): boolean => {
        return role === UserRole.admin;
      };

      const hasManagerAccess = (role: UserRole): boolean => {
        return role === UserRole.admin || role === UserRole.mall_manager;
      };

      expect(hasAdminAccess(UserRole.admin)).toBe(true);
      expect(hasAdminAccess(UserRole.user)).toBe(false);
      
      expect(hasManagerAccess(UserRole.admin)).toBe(true);
      expect(hasManagerAccess(UserRole.mall_manager)).toBe(true);
      expect(hasManagerAccess(UserRole.shop_owner)).toBe(false);
    });
  });

  describe('ReportType enum', () => {
    it('should have correct values', () => {
      expect(ReportType.MALL_ANALYTICS).toBe('MALL_ANALYTICS');
      expect(ReportType.SHOP_ANALYTICS).toBe('SHOP_ANALYTICS');
      expect(ReportType.USER_ANALYTICS).toBe('USER_ANALYTICS');
      expect(ReportType.PAGE_VIEW_ANALYTICS).toBe('PAGE_VIEW_ANALYTICS');
    });

    it('should have all expected keys', () => {
      const keys = Object.keys(ReportType);
      expect(keys).toContain('MALL_ANALYTICS');
      expect(keys).toContain('SHOP_ANALYTICS');
      expect(keys).toContain('USER_ANALYTICS');
      expect(keys).toContain('PAGE_VIEW_ANALYTICS');
      expect(keys).toHaveLength(4);
    });

    it('should be usable in report filtering', () => {
      const getReportTitle = (type: ReportType): string => {
        switch (type) {
          case ReportType.MALL_ANALYTICS:
            return 'Mall Analytics Report';
          case ReportType.SHOP_ANALYTICS:
            return 'Shop Analytics Report';
          case ReportType.USER_ANALYTICS:
            return 'User Analytics Report';
          case ReportType.PAGE_VIEW_ANALYTICS:
            return 'Page View Analytics Report';
          default:
            return 'Unknown Report';
        }
      };

      expect(getReportTitle(ReportType.MALL_ANALYTICS)).toBe('Mall Analytics Report');
      expect(getReportTitle(ReportType.SHOP_ANALYTICS)).toBe('Shop Analytics Report');
      expect(getReportTitle(ReportType.USER_ANALYTICS)).toBe('User Analytics Report');
      expect(getReportTitle(ReportType.PAGE_VIEW_ANALYTICS)).toBe('Page View Analytics Report');
    });
  });

  describe('$Enums object', () => {
    it('should contain all enum types', () => {
      expect($Enums).toBeDefined();
      expect($Enums.ParkingType).toBeDefined();
      expect($Enums.UserRole).toBeDefined();
      expect($Enums.ReportType).toBeDefined();
    });

    it('should have matching values with individual enums', () => {
      expect($Enums.ParkingType).toEqual(ParkingType);
      expect($Enums.UserRole).toEqual(UserRole);
      expect($Enums.ReportType).toEqual(ReportType);
    });
  });

  describe('Enum combinations and usage', () => {
    it('should work in complex type scenarios', () => {
      interface MallConfig {
        parkingType: ParkingType;
        managerRole: UserRole;
        reportTypes: ReportType[];
      }

      const config: MallConfig = {
        parkingType: ParkingType.paid,
        managerRole: UserRole.mall_manager,
        reportTypes: [ReportType.MALL_ANALYTICS, ReportType.SHOP_ANALYTICS]
      };

      expect(config.parkingType).toBe('paid');
      expect(config.managerRole).toBe('mall_manager');
      expect(config.reportTypes).toContain('MALL_ANALYTICS');
      expect(config.reportTypes).toContain('SHOP_ANALYTICS');
    });

    it('should support enum validation functions', () => {
      const validateUserRole = (role: string): role is UserRole => {
        return Object.values(UserRole).includes(role as UserRole);
      };

      const validateParkingType = (type: string): type is ParkingType => {
        return Object.values(ParkingType).includes(type as ParkingType);
      };

      expect(validateUserRole('admin')).toBe(true);
      expect(validateUserRole('invalid_role')).toBe(false);
      expect(validateParkingType('free')).toBe(true);
      expect(validateParkingType('invalid_type')).toBe(false);
    });
  });
});
