import { describe, it, expect, jest } from '@jest/globals';

// Mock dependencies before importing the main module
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  }))
}));

jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn()
  }
}));

jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  }))
}));

describe('Main Index Exports', () => {
  it('should export prisma client', async () => {
    const { default: prisma } = await import('../src/database/prisma');
    expect(prisma).toBeDefined();
    expect(prisma.$connect).toBeDefined();
  });

  it('should export API utilities', async () => {
    const { ApiError, errorHandler, apiClient } = await import('../src/index');
    
    expect(ApiError).toBeDefined();
    expect(errorHandler).toBeDefined();
    expect(apiClient).toBeDefined();
    
    // Test ApiError instantiation
    const error = new ApiError(400, 'Test error');
    expect(error).toBeInstanceOf(Error);
    expect(error.statusCode).toBe(400);
    expect(error.message).toBe('Test error');
  });

  it('should export utility functions', async () => {
    const { cn } = await import('../src/index');
    
    expect(cn).toBeDefined();
    expect(typeof cn).toBe('function');
    
    // Test cn function
    const result = cn('class1', 'class2');
    expect(result).toBe('class1 class2');
  });

  it('should export type definitions', async () => {
    // Import types to ensure they exist
    const module = await import('../src/index');
    
    // These are type-only exports, so we can't test them at runtime
    // But we can ensure the module imports without errors
    expect(module).toBeDefined();
  });

  it('should export Prisma enums and types', async () => {
    const { 
      ParkingType, 
      ReportType, 
      UserRole,
      $Enums 
    } = await import('../src/index');
    
    expect(ParkingType).toBeDefined();
    expect(ReportType).toBeDefined();
    expect(UserRole).toBeDefined();
    expect($Enums).toBeDefined();
    
    // Test enum values
    expect(ParkingType.free).toBe('free');
    expect(ParkingType.paid).toBe('paid');
    expect(ParkingType.valet).toBe('valet');
    
    expect(UserRole.admin).toBe('admin');
    expect(UserRole.mall_manager).toBe('mall_manager');
    expect(UserRole.shop_owner).toBe('shop_owner');
    expect(UserRole.user).toBe('user');
    
    expect(ReportType.MALL_ANALYTICS).toBe('MALL_ANALYTICS');
    expect(ReportType.SHOP_ANALYTICS).toBe('SHOP_ANALYTICS');
    expect(ReportType.USER_ANALYTICS).toBe('USER_ANALYTICS');
    expect(ReportType.PAGE_VIEW_ANALYTICS).toBe('PAGE_VIEW_ANALYTICS');
  });

  it('should have all expected exports available', async () => {
    const module = await import('../src/index');
    
    // Check that key exports are present
    const expectedExports = [
      'prisma',
      'ApiError',
      'errorHandler',
      'apiClient',
      'cn',
      'ParkingType',
      'ReportType',
      'UserRole',
      '$Enums'
    ];
    
    expectedExports.forEach(exportName => {
      expect(module).toHaveProperty(exportName);
    });
  });

  it('should not have any undefined exports', async () => {
    const module = await import('../src/index');
    
    // Get all enumerable properties
    const exportNames = Object.keys(module);
    
    exportNames.forEach(exportName => {
      expect(module[exportName as keyof typeof module]).toBeDefined();
    });
  });
});
