import { describe, it, expect, jest } from '@jest/globals';
import { ApiError, errorHandler } from '../../src/lib/api-error';

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: jest.fn((body, options) => ({
      body,
      status: options?.status || 200,
      headers: options?.headers || {}
    }))
  }
}));

describe('API Error Handling', () => {
  describe('ApiError class', () => {
    it('should create an ApiError with correct properties', () => {
      const error = new ApiError(400, 'Bad Request');
      
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(ApiError);
      expect(error.statusCode).toBe(400);
      expect(error.message).toBe('Bad Request');
      expect(error.isOperational).toBe(true);
      expect(error.stack).toBeDefined();
    });

    it('should create an ApiError with custom operational flag', () => {
      const error = new ApiError(500, 'Internal Error', false);
      
      expect(error.statusCode).toBe(500);
      expect(error.message).toBe('Internal Error');
      expect(error.isOperational).toBe(false);
    });

    it('should create an ApiError with custom stack', () => {
      const customStack = 'Custom stack trace';
      const error = new ApiError(404, 'Not Found', true, customStack);
      
      expect(error.statusCode).toBe(404);
      expect(error.message).toBe('Not Found');
      expect(error.stack).toBe(customStack);
    });

    it('should capture stack trace when no custom stack provided', () => {
      const error = new ApiError(403, 'Forbidden');
      
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('ApiError');
    });
  });

  describe('errorHandler function', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      // Mock console.error to avoid noise in tests
      jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should handle ApiError correctly', () => {
      const apiError = new ApiError(400, 'Validation failed');
      const result = errorHandler(apiError);
      
      expect(result.body).toEqual({
        success: false,
        error: 'Validation failed'
      });
      expect(result.status).toBe(400);
    });

    it('should handle unknown errors', () => {
      const unknownError = new Error('Unknown error');
      const result = errorHandler(unknownError);
      
      expect(result.body).toEqual({
        success: false,
        error: 'Internal Server Error'
      });
      expect(result.status).toBe(500);
      expect(console.error).toHaveBeenCalledWith('Unexpected error:', unknownError);
    });

    it('should handle string errors', () => {
      const stringError = 'Something went wrong';
      const result = errorHandler(stringError);
      
      expect(result.body).toEqual({
        success: false,
        error: 'Internal Server Error'
      });
      expect(result.status).toBe(500);
      expect(console.error).toHaveBeenCalledWith('Unexpected error:', stringError);
    });

    it('should handle null/undefined errors', () => {
      const result1 = errorHandler(null);
      const result2 = errorHandler(undefined);
      
      expect(result1.body).toEqual({
        success: false,
        error: 'Internal Server Error'
      });
      expect(result1.status).toBe(500);
      
      expect(result2.body).toEqual({
        success: false,
        error: 'Internal Server Error'
      });
      expect(result2.status).toBe(500);
    });
  });
});
