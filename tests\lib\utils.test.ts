import { describe, it, expect } from '@jest/globals';
import { cn } from '../../src/lib/utils';

describe('Utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('px-2 py-1', 'bg-blue-500');
      expect(result).toBe('px-2 py-1 bg-blue-500');
    });

    it('should handle conditional classes', () => {
      const result = cn('base-class', true && 'conditional-class', false && 'hidden-class');
      expect(result).toBe('base-class conditional-class');
    });

    it('should handle arrays of classes', () => {
      const result = cn(['class1', 'class2'], 'class3');
      expect(result).toBe('class1 class2 class3');
    });

    it('should handle objects with boolean values', () => {
      const result = cn({
        'active': true,
        'disabled': false,
        'primary': true
      });
      expect(result).toBe('active primary');
    });

    it('should merge conflicting Tailwind classes correctly', () => {
      const result = cn('px-2 px-4', 'py-1 py-2');
      expect(result).toBe('px-4 py-2');
    });

    it('should handle empty inputs', () => {
      const result = cn();
      expect(result).toBe('');
    });

    it('should handle null and undefined inputs', () => {
      const result = cn('valid-class', null, undefined, 'another-class');
      expect(result).toBe('valid-class another-class');
    });

    it('should handle complex mixed inputs', () => {
      const result = cn(
        'base',
        ['array1', 'array2'],
        { conditional: true, hidden: false },
        true && 'conditional-string',
        null,
        undefined,
        'final'
      );
      expect(result).toBe('base array1 array2 conditional conditional-string final');
    });
  });
});
